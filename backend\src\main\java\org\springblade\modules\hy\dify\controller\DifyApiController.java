package org.springblade.modules.hy.dify.controller;

import cn.hutool.core.util.StrUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.modules.hy.dify.pojo.dto.DifyRequestBody;
import org.springblade.modules.hy.dify.pojo.vo.BlockResponse;
import org.springblade.modules.hy.dify.service.IDifyService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Dify API控制器
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@RestController
@RequestMapping("/dify")
@RequiredArgsConstructor
public class DifyApiController extends BladeController {

    private final IDifyService difyService;

    /**
     * 阻塞式调用Dify对话接口
     *
     * @param query          查询内容
     * @param conversationId 会话ID（可选）
     * @return 对话响应
     */
    @GetMapping("/block")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "阻塞式对话", description = "发送消息到Dify并等待完整响应")
    public R<BlockResponse> blockApi(
            @Parameter(description = "查询内容", required = true) @RequestParam String query,
            @Parameter(description = "会话ID") @RequestParam(required = false) String conversationId) {

        try {
            // 获取当前用户信息
            BladeUser user = AuthUtil.getUser();
            if (user == null) {
                return R.fail("用户未登录");
            }

            // 参数校验
            if (StrUtil.isBlank(query)) {
                return R.fail("查询内容不能为空");
            }

            log.info("用户 {} 发起Dify对话请求，查询内容: {}", user.getUserId(), query);

            // 调用服务
            BlockResponse response = difyService.blockingMessage(query, user.getUserId(), conversationId);

            return R.data(response);

        } catch (Exception e) {
            log.error("Dify对话请求失败: {}", e.getMessage(), e);
            return R.fail("对话请求失败: " + e.getMessage());
        }
    }

    /**
     * 阻塞式调用Dify对话接口（POST方式）
     *
     * @param requestBody 请求体
     * @return 对话响应
     */
    @PostMapping("/chat")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "阻塞式对话（POST）", description = "通过POST方式发送消息到Dify并等待完整响应")
    public R<BlockResponse> chatApi(
            @Parameter(description = "请求体", required = true) @Valid @RequestBody DifyRequestBody requestBody) {

        try {
            // 获取当前用户信息
            BladeUser user = AuthUtil.getUser();
            if (user == null) {
                return R.fail("用户未登录");
            }

            // 参数校验
            if (StrUtil.isBlank(requestBody.getQuery())) {
                return R.fail("查询内容不能为空");
            }

            log.info("用户 {} 发起Dify对话请求（POST），查询内容: {}", user.getUserId(), requestBody.getQuery());

            // 调用服务
            BlockResponse response = difyService.blockingMessage(
                    requestBody.getQuery(),
                    user.getUserId(),
                    requestBody.getConversationId(),
                    requestBody.getInputs()
            );

            return R.data(response);

        } catch (Exception e) {
            log.error("Dify对话请求失败: {}", e.getMessage(), e);
            return R.fail("对话请求失败: " + e.getMessage());
        }
    }

}
