/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.dify.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.hy.dify.mapper.AiChatLogMapper;
import org.springblade.modules.hy.dify.pojo.entity.AiChatLogEntity;
import org.springblade.modules.hy.dify.service.IAiChatLogService;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * AI会务助手问答日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Service
@Slf4j
public class AiChatLogServiceImpl extends ServiceImpl<AiChatLogMapper, AiChatLogEntity> implements IAiChatLogService {

	/**
	 * 匹配<think>标签的正则表达式
	 */
	private static final Pattern THINK_TAG_PATTERN = Pattern.compile("<think>.*?</think>", Pattern.DOTALL);

	@Override
	public boolean saveChatLog(Long userId, String question, String answer) {
		try {
			// 参数校验
			if (userId == null || StringUtil.isBlank(question)) {
				log.warn("保存聊天记录失败：用户ID或问题内容为空");
				return false;
			}

			// 过滤掉回答中的<think>标签内容
			String filteredAnswer = filterThinkTags(answer);

			// 创建聊天记录实体
			AiChatLogEntity chatLog = new AiChatLogEntity();
			chatLog.setUserId(userId);
			chatLog.setQuestion(question);
			chatLog.setAnswer(filteredAnswer);

			// 保存到数据库
			boolean result = this.saveOrUpdate(chatLog);
			
			if (result) {
				log.info("聊天记录保存成功，用户ID: {}, 问题: {}", userId, question);
			} else {
				log.error("聊天记录保存失败，用户ID: {}, 问题: {}", userId, question);
			}
			
			return result;
		} catch (Exception e) {
			log.error("保存聊天记录异常，用户ID: {}, 问题: {}, 错误信息: {}", userId, question, e.getMessage(), e);
			return false;
		}
	}

	/**
	 * 过滤掉回答中的<think>标签内容
	 *
	 * @param answer 原始回答
	 * @return 过滤后的回答
	 */
	private String filterThinkTags(String answer) {
		if (StringUtil.isBlank(answer)) {
			return answer;
		}
		
		// 使用正则表达式移除<think>标签及其内容
		return THINK_TAG_PATTERN.matcher(answer).replaceAll("").trim();
	}

}
