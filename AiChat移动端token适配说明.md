# AiChat移动端token适配说明

## 修改概述

将AiChat.vue组件从使用普通的API请求方式改为使用移动端专用的mobileRequest，以便正确使用移动端的token进行用户身份认证，确保聊天记录能够正确关联到具体用户。

## 修改内容

### 1. 导入模块修改
**修改前：**
```javascript
import { getDictionary } from '@/api/system/dictbiz';
import { sendChatMessage } from '@/api/dify';
```

**修改后：**
```javascript
import { getDictionary } from '@/api/system/dictbiz';
import { mobileRequest, checkMobileAuth } from '@/utils/mobileRequest';
```

### 2. 组件挂载时添加登录检查
**修改前：**
```javascript
async mounted() {
  await this.loadQuickQuestions();
},
```

**修改后：**
```javascript
async mounted() {
  // 检查移动端登录状态
  if (!checkMobileAuth()) {
    this.$message && this.$message.error('请先登录');
    return;
  }
  await this.loadQuickQuestions();
},
```

### 3. 发送消息方法修改
**修改前：**
```javascript
async sendMessage(predefinedMessage = null) {
  // ... 其他代码

  const message = predefinedMessage || this.currentMessage.trim();
  if (!message) return;

  // ... 其他代码

  try {
    // 调用Dify API
    const response = await sendChatMessage({
      query: message,
      conversationId: this.conversationId,
      inputs: {}
    });
    // ... 处理响应
  }
}
```

**修改后：**
```javascript
async sendMessage(predefinedMessage = null) {
  // ... 其他代码

  // 检查移动端登录状态
  if (!checkMobileAuth()) {
    this.$message && this.$message.error('请先登录');
    return;
  }

  const message = predefinedMessage || this.currentMessage.trim();
  if (!message) return;

  // ... 其他代码

  try {
    // 调用Dify API
    const response = await mobileRequest({
      method: 'post',
      url: '/dify/chat',
      data: {
        query: message,
        conversationId: this.conversationId,
        inputs: {}
      }
    });
    // ... 处理响应
  }
}
```

## 修改效果

### 1. 身份认证
- 使用移动端专用的token认证机制
- 自动在请求头中添加`Blade-Auth`、`Tenant-Id`等必要的认证信息
- 支持token自动刷新机制

### 2. 用户关联
- 后端能够正确识别当前登录用户
- 聊天记录会正确关联到具体的用户ID
- 确保数据安全和用户隔离

### 3. 错误处理
- 在组件挂载和发送消息时都会检查登录状态
- 未登录时会显示友好的错误提示
- 401错误时会自动尝试刷新token

### 4. 兼容性
- 保持原有的响应数据格式处理逻辑不变
- 保持原有的错误处理和兜底机制
- 保持原有的用户界面和交互体验

## 技术细节

### mobileRequest的特点
1. **自动添加认证头**：包含Bearer token、租户ID等
2. **自动重试机制**：401错误时自动刷新token并重试
3. **统一错误处理**：提供一致的错误处理机制
4. **移动端优化**：专门为移动端环境设计

### checkMobileAuth的作用
1. **登录状态检查**：验证用户是否已登录
2. **token有效性**：检查accessToken是否存在
3. **快速失败**：避免无效请求的发送

## 注意事项

1. **依赖关系**：确保`@/utils/mobileRequest`和相关的用户store正常工作
2. **后端兼容**：后端的`/dify/chat`接口需要支持移动端的认证机制
3. **错误处理**：保持原有的兜底回复机制，确保用户体验
4. **测试验证**：需要在移动端环境中测试登录状态检查和API调用

## 预期结果

修改完成后，AiChat组件将：
1. 正确使用移动端token进行身份认证
2. 聊天记录能够准确关联到具体用户
3. 提供更好的安全性和用户体验
4. 与其他移动端组件保持一致的认证机制
